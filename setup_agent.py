from langchain_core.messages import ToolMessage
from langchain_core.runnables import RunnableLamb<PERSON>
from langgraph.prebuilt import ToolNode
from langchain_groq import ChatGroq
import groq
from langchain_core.prompts import ChatPromptTemplate
from langgraph.prebuilt import create_react_agent
from langgraph.prebuilt.chat_agent_executor import AgentState
# from langchain_ollama import ChatOllama
from typing import Annotated
from datetime import datetime, date
from toolsAgent import find_customer, make_order, get_products, fund_customer
from langgraph.graph.message import AnyMessage, add_messages
from typing_extensions import TypedDict
from langchain_core.messages import  HumanMessage,AIMessage

class State(TypedDict):
    messages: Annotated[list[AnyMessage], add_messages]
    user_info: str
    tool_query: str


def handle_tool_error(state) -> dict:
    error = state.get("error")
    print("🔴 TOOL ERROR:", error)
    tool_calls = state["messages"][-1].tool_calls
    return {
        "messages": [
            ToolMessage(
                content=f"Error: {repr(error)}\n please fix your mistakes.",
                tool_call_id=tc["id"],
            )
            for tc in tool_calls
        ]
    }


def create_tool_node_with_fallback(tools: list) -> dict:
    return ToolNode(tools).with_fallbacks(
        [RunnableLambda(handle_tool_error)], exception_key="error"
    )







class Assistant:
    def __init__(self, runnable):
        self.runnable = runnable

    def __call__(self, state, config):
        # Extract user and thread info from config.
        while True:
            configuration = config.get("configurable", {})
            user_id = configuration.get("user_id", None)
            thread_id = configuration["thread_id"]
            user_input = configuration.get("user_input", "")


            state = {**state, "user_info": user_id}






                            # Use the store (if provided) to store the full conversation history.
            store = config["configurable"].get("__pregel_store")

            if store is not None:
                # Build the conversation history from all messages.
                conversation_history = "\n".join(
                    [
                        msg.content if hasattr(msg, "content") and msg.content else str(msg)
                        for msg in state["messages"]
                    ]
                )
            else:
                conversation_history = ""

                # Save the full conversation history with a unique key.

                # Note: We store the history for persistence but DO NOT append it to the response.
            #print("message",messages)
            # Invoke the assistant's runnable chain with the current state.

            # print("con",conversation_history)
            try:
                    result = self.runnable.invoke({
                    "messages": messages,
                    "user_info": user_id,
                    "conversation_history": conversation_history
                    })

                    # If the result is a tool message, return it immediately.
                    if isinstance(result, ToolMessage):

                        messages = messages + [result]
                        return {"messages": messages, "user_info": user_id}
                    if "<tool-use>" in result.content:
                        # Do something
                        messages = messages + [HumanMessage(content="Respond with a real output using ur own knowledge."+user_input)]
                        result =self.runnable.invoke({"messages": messages, "user_info": user_id,"conversation_history": conversation_history})
                    # if not result.tool_calls:
                    #     if(result.content):
                    #         print("result",result.content)
                    #     else:
                    #         print("result",result['output'])
                    # Ensure a valid result if the output is empty.
                    if not result.tool_calls and (not result.content or (isinstance(result.content, list) and not result.content[0].get("text"))):
                    # if not result :
                        print("tool not called")
                        messages = messages + [HumanMessage(content="Respond with a real output.")]
                        result =self.runnable.invoke({"messages": messages, "user_info": user_id,"conversation_history": conversation_history})
                    else:
                        messages = messages + [result]
                        break
            except groq.BadRequestError as e:
                print("Caught BadRequestError:")
                print("Full exception:", e)
                print("Type:", type(e))
                print("Dir:", dir(e))
                print("Error: The message length exceeded the allowed context length.",e)
                if hasattr(e, 'body') and isinstance(e.body, dict):
                  if e.body.get('error', {}).get('code') == 'context_length_exceeded':
                    print("Error: The message length exceeded the allowed context length.")
                    conversation_history = conversation_history[-5:] if len(conversation_history) > 5 else conversation_history
                    print("conversation_history length",len(conversation_history))
                    print("messages length",len(messages))
                    result = self.runnable.invoke({
                    "messages": messages,
                    "user_info": user_id,
                    "conversation_history": conversation_history
                })

                    # If the result is a tool message, return it immediately.
                    if isinstance(result, ToolMessage):
                        print("tool",result)
                        messages = messages + [result]
                        return {"messages": messages, "user_info": user_id}
                    if "<tool-use>" in result.content:
                        # Do something
                        messages = messages + [HumanMessage(content="Respond with a real output using."+user_input)]
                        result =self.runnable.invoke({"messages": messages, "user_info": user_id,"conversation_history": conversation_history})
                    # if not result.tool_calls:
                    #     if(result.content):
                    #         print("result",result.content)
                    #     else:
                    #         print("result",result['output'])
                    # Ensure a valid result if the output is empty.
                    if not result.tool_calls and (not result.content or (isinstance(result.content, list) and not result.content[0].get("text"))):
                    # if not result :
                        print("tool not called")
                        messages = messages + [HumanMessage(content="Respond with a real output.")]
                        result =self.runnable.invoke({"messages": messages, "user_info": user_id,"conversation_history": conversation_history})
                    else:
                        messages = messages + [result]
                        break
                else:
                    print(f"BadRequestError occurred: {e}")
                    return {"messages": messages + [AIMessage(content=f"ahh i can't gotch u please say it again.")]}
                break
            except Exception as e:

                print("length",len(conversation_history))
                print("Error:", e)
                return {"messages": messages + [AIMessage(content=f"sorry for inconvenience. i can't gotch u please say it again.")]}



        try:
             return {"messages": messages +[result]}
        except Exception as e:
            print("Error:", e)
            return {"messages": [AIMessage(content=f"Server is busy. Please try again later.")]}




local_llm = "llama3-70b-8192"
llm =ChatGroq(model=local_llm,groq_api_key="********************************************************" ,temperature=0.7,max_retries=2)

primary_assistant_prompt = ChatPromptTemplate.from_messages([
    (
        "system",
        """

**Conversation Context**:
- Conversation History: {conversation_history}
- Current User ID: {user_info}
- Time: {time} | Date: {date} | Day: {day}"""
    ),
    ("placeholder", "{messages}"),
]).partial(time=datetime.now(), date=date.today(), day=datetime.today().strftime("%A"))
# vector_store, _, _ = load_all_vsl(1)
#     docs =  vector_store.as_retriever(search_kwargs={"k": 3}).get_relevant_documents(query)
#     context = "\n".join([doc.page_content for doc in docs])
#     return {"query": query, "context": context}

part_1_tools = [
    make_order,get_products, find_customer
]
part_1_assistant_runnable = primary_assistant_prompt | llm.bind_tools(part_1_tools )

from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import StateGraph,START, END
from langgraph.prebuilt import tools_condition
from langchain_core.runnables import RunnableConfig

builder = StateGraph(State)


def user_info(state: State):
    """
    Initialize customer verification and service process.
    This function creates an agent that handles customer verification through general conversation
    and provides access to customer services including funding once verified.
    """
    # Create a comprehensive agent with verification and service tools
    verification_agent = create_react_agent(
        llm,
        tools=[find_customer, fund_customer],
        prompt="""
You are a customer service assistant. Your job is to handle customer verification through natural conversation and provide access to customer services once verified.

IMPORTANT INSTRUCTIONS:
1. If the customer is not yet verified, politely ask for their full name (first and last name) and use the find_customer tool to verify their identity.
2. Once verified, you can help them with various services including account funding using the fund_customer tool.
3. The fund_customer tool requires the customer to be verified first and takes first_name and last_name parameters.
4. Always check if the customer is verified before allowing access to services.
5. Be friendly, professional, and conversational.
6. If the user asks about funding or account services, first ensure they are verified.

VERIFICATION PROCESS:
- Ask for full name (first and last name) if not provided
- Use find_customer tool to verify identity
- Once verified, inform them they can access services

SERVICE PROCESS (only after verification):
- If customer asks about funding, use fund_customer tool with their verified name
- Always use the exact name that was verified

Example conversations:
Scenario 1 - New customer:
Assistant: Hello! Welcome to our service. How can I help you today?
User: I'd like to fund my account
Assistant: I'd be happy to help you with account funding! First, I need to verify your identity. Could you please provide your full name (first and last name)?

Scenario 2 - Customer provides name:
User: My name is John Smith
Assistant: [Use find_customer tool with first_name="John", last_name="Smith"]

Scenario 3 - Verified customer wants funding:
User: I want to fund my account
Assistant: [Use fund_customer tool with the verified customer's first_name and last_name]
"""
    )

    # Get the messages from the state
    messages = state.get("messages", [])

    # Check if we already have customer info and they're verified
    context_obj = state.get("context", {}).get("_obj")
    print("context_obj",context_obj)
    if context_obj and hasattr(context_obj, "userdata") and hasattr(context_obj.userdata, "customer_id"):
        # Customer is already verified, agent can handle both verification status and services
        return {
            "messages": messages,
            "user_info": f"Customer ID: {context_obj.userdata.customer_id}, Name: {context_obj.userdata.customer_name}",
            "customer_verified": True,
            "customer_name": context_obj.userdata.customer_name,
            "customer_id": context_obj.userdata.customer_id
        }

    # If no messages yet, add a greeting from the agent
    if not messages:
        greeting = AIMessage(
            content="Hello! Welcome to our service. How can I help you today?"
        )
        return {"messages": [greeting], "user_info": None}

    try:
        # Get config from state or create a default one
        config = state.get("config", {})

        # Invoke the verification agent with the current messages
        print("Invoking verification agent...")
        result = verification_agent.invoke({"messages": messages, "config": config})
        print(f"Verification agent result: {result}")

        print("=== STATE AND CONFIG DEBUG ===")
        print(f"State: {state}")
        print(f"Config: {config}")
        print("===============================")

        # Check if customer is verified through state
        if state.get("customer_verified") == True:
            print("Customer is verified")

        # Check if customer was verified by the tool through context
        context_obj = state.get("context", {}).get("_obj")
        if context_obj and hasattr(context_obj, "userdata") and hasattr(context_obj.userdata, "customer_id"):
            print(f"\nSystem: Customer verified: {context_obj.userdata.customer_name}")

        # Return the agent's response
        return {
            "messages": result.get("messages", messages),
            "user_info": None
        }

    except Exception as e:
        print(f"Error in user_info: {str(e)}")
        return {
            "messages": messages + [AIMessage(content="I'm having trouble verifying your information. Could you please provide your full name (first and last name)?")],
            "user_info": None
        }

    # Default return if no processing occurred
    return {"messages": messages, "user_info": None}


# NEW: The fetch_user_info node runs first, meaning our assistant can see the user's flight information without
# having to take an action
builder.add_node("fetch_user_info", user_info)
builder.add_edge(START, "fetch_user_info")
builder.add_node("assistant", Assistant(part_1_assistant_runnable))
builder.add_node("tools", create_tool_node_with_fallback(part_1_tools))
builder.add_edge("fetch_user_info", "assistant")
builder.add_conditional_edges(
    "assistant",
    tools_condition,
)
builder.add_edge("tools", "assistant")

memory = MemorySaver()
part_2_graph = builder.compile(
    checkpointer=memory,
    # NEW: The graph will always halt before executing the "tools" node.
    # The user can approve or reject (or even alter the request) before
    # the assistant continues
    interrupt_before=["tools"],
)

import shutil
import uuid

# Let's create an example conversation a user might have with the assistant


# Update with the backup file so we can restart from the original place in each section

thread_id = str(uuid.uuid4())

config = {
    "configurable": {


        # Checkpoints are accessed by thread_id
        "thread_id": thread_id,
    }
}



def main():
    """
    Main function to run the agent in a conversation loop.
    """
    print("Welcome to our customer service system!")
    print("Type 'exit' to end the conversation.")

    # Generate a unique thread ID for this conversation
    thread_id = str(uuid.uuid4())

    # Create a context object for tools to use
    context_obj = type('obj', (object,), {})
    context_obj.userdata = type('obj', (object,), {})
    context_obj.state = {}  # Will hold state information

    # Initialize configuration
    config = {
        "configurable": {
            "user_id": None,  # Will be updated with customer info
            "thread_id": thread_id,
            "context": context_obj,  # Pass the context object to tools
            "__pregel_store": {}  # For storing conversation history
        }
    }

    # Initialize state
    state = {
        "messages": [],
        "user_info": None,
        "context": {
            "_obj": context_obj  # Make context accessible to tools
        }
    }

    # Track printed messages to avoid duplicates
    _printed = set()

    # Main conversation loop
    while True:
        # Get user input
        user_input = input("\nYou: ")

        # Check if user wants to exit
        if user_input.lower() in ['exit', 'quit', 'bye']:
            print("Thank you for using our service. Goodbye!")
            break

        # Update config with user input
        config["configurable"]["user_input"] = user_input

        try:
            # Process the user input through the graph
            events = part_2_graph.stream(
                {"messages": ("user", user_input)},
                config,
                stream_mode="values"
            )

            # Print each event in the stream
            for event in events:
                _print_event(event, _printed)

                # Update state with the latest event
                state.update(event)

                # Update context object with state information
                context_obj.state = state

                # Update user_id in config if available
                if state.get("user_info"):
                    config["configurable"]["user_id"] = state["user_info"]

                # Check if customer was verified by the tool
                if hasattr(context_obj.userdata, "customer_id"):
                    state["customer_verified"] = True
                    state["customer_name"] = context_obj.userdata.customer_name
                    state["customer_id"] = context_obj.userdata.customer_id
                    print(f"\nSystem: Customer verified: {context_obj.userdata.customer_name}")

        except Exception as e:
            print(f"\nError: {str(e)}")
            print("Please try again.")

    return "Conversation completed"

# Function to print events
def _print_event(event, printed):
    """
    Print messages from an event, avoiding duplicates.
    """
    messages = event.get("messages", [])
    for i, message in enumerate(messages):
        # Create a hashable identifier for the message
        message_id = id(message)

        # Skip already printed messages
        if message_id in printed:
            continue

        # Print message content
        if hasattr(message, "content"):
            if hasattr(message, "name") and message.name:
                print(f"\n{message.name}: {message.content}")
            else:
                role = "AI" if not hasattr(message, "type") or message.type != "human" else "You"
                print(f"\n{role}: {message.content}")

        # Add message ID to printed set
        printed.add(message_id)

# Run the main function when script is executed directly
if __name__ == "__main__":
    main()
