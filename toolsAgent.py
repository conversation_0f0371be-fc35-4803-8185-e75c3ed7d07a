from typing import Any, Dict
from langchain_core.tools import tool
import requests
import logging
import os
from langchain_core.runnables import RunnableConfig
# Set up logging
logging.basicConfig(level=logging.INFO)

# Define RunContext_T for tools
class RunContext_T:
    """Type for tool context"""
    pass

@tool
def find_customer(first_name: str, last_name: str, config:RunnableConfig) -> str:
    """Call the database to find the customer's information."""
    logging.info(f"Calling find_customer for name: {first_name} {last_name}")
    try:
        # Extract context from config
        context = config.get("configurable", {}).get("context")
        if not context:
            logging.warning("No context found in config")
            context = type('obj', (object,), {})



        logging.info(f"Making API request for {first_name} {last_name}")
        response = requests.get(f'http://localhost:5000/customers/search?firstName={first_name}&lastName={last_name}')
        logging.info(f"API response status: {response.status_code}")

        if response.status_code == 400:
            return "Please provide both first name and last name to search for a customer."
        elif response.status_code == 200:
            data = response.json()
            logging.info(f"API response data: {data}")

            if not data.get('isNewCustomer'):
                # Customer found
                customer = data['customer']

                # Update context with customer info
                context.userdata.customer_name = f"{customer['contactFirstName']} {customer['contactLastName']}"
                context.userdata.customer_phone = customer.get('phone', '')
                context.userdata.customer_id = customer['customerNumber']

                # Update Agent state directly
                if hasattr(context, 'state'):
                    context.state["customer_verified"] = True
                    context.state["customer_name"] = context.userdata.customer_name
                    context.state["customer_id"] = context.userdata.customer_id

                logging.info(f"Customer verified: {context.userdata.customer_name} (ID: {context.userdata.customer_id})")
                return f"Customer verified: {context.userdata.customer_name}. You can now proceed to speak with our Sales Agent."
            else:
                # New customer created
                if 'customer' in data:
                    customer = data['customer']

                    # Update context with customer info
                    context.userdata.customer_name = f"{customer['contactFirstName']} {customer['contactLastName']}"
                    context.userdata.customer_phone = customer.get('phone', '')
                    context.userdata.customer_id = customer['customerNumber']

                    # Update Agent state directly
                    if hasattr(context, 'state'):
                        context.state["customer_verified"] = True
                        context.state["customer_name"] = context.userdata.customer_name
                        context.state["customer_id"] = context.userdata.customer_id

                    logging.info(f"New customer created: {context.userdata.customer_name} (ID: {context.userdata.customer_id})")
                    return f"New customer created: {context.userdata.customer_name}. You can now proceed to speak with our Sales Agent."
                else:
                    return "I couldn't find your name in our records. Would you like to proceed as a new customer?"
        else:
            return f"Error: Received status code {response.status_code} while searching for the customer. Please try again."

    except requests.RequestException as e:
        logging.error(f"Error connecting to customer database: {str(e)}")
        return f"Error connecting to the customer database: {str(e)}"

@tool
def make_order(product_name: str, quantity: int, config) -> str:
    """Process an order for the customer."""
    logging.info(f"Calling make_order for product: {product_name}, quantity: {quantity}")
    try:
        # Extract context from config
        context = config.get("configurable", {}).get("context")
        if not context:
            logging.warning("No context found in config")
            context = type('obj', (object,), {})

        # Initialize userdata if not present
        if not hasattr(context, "userdata"):
            context.userdata = type('obj', (object,), {})

        # Check if customer is verified
        if not hasattr(context.userdata, "customer_id"):
            return "Please verify your identity before placing an order."

        # Prepare order data
        order_data = {
            'customerNumber': context.userdata.customer_id,
            'productName': product_name,
            'quantity': quantity
        }
        # Make request to create order
        response = requests.post('http://localhost:5000/orders', json=order_data)

        if response.status_code == 201:
            order_info = response.json()
            return f"Great! Your order has been placed successfully. Your order number is {order_info['orderNumber']}. Is there anything else you'd like to order?"
        elif response.status_code == 400:
            error = response.json().get('error', 'Invalid order data')
            return f"I apologize, but there was an issue with the order: {error}. Please try again with valid information."
        elif response.status_code == 404:
            return f"I apologize, but the product '{product_name}' was not found. Please select a different product."
        else:
            return f"Error: Received status code {response.status_code} while processing your order. Please try again."

    except requests.RequestException as e:
        logging.error(f"Error connecting to order system: {str(e)}")
        return f"Error connecting to the order system: {str(e)}"

@tool
def get_products(product_line: str, config) -> str:
    """Get all products from a specific product line/category."""
    logging.info(f"Calling get_products for category: {product_line}")
    try:
        # Extract context from config
        context = config.get("configurable", {}).get("context")
        if not context:
            logging.warning("No context found in config")
            context = type('obj', (object,), {})

        # Make request to get products by category
        response = requests.get(f'http://localhost:5000/products/{product_line}')

        if response.status_code == 404:
            return f"No products found in the {product_line} category. Please select another category."
        elif response.status_code == 200:
            products = response.json()
            if not products:
                return f"No products available in {product_line} category. Please select another category."

            # Format products for display
            product_list = []
            for product in products:
                product_list.append(f"- Product Name: {product['productName']} - Description: {product['productDescription']} - Price: ${product['MSRP']}")

            return f"Here are the products in {product_line} category:\n" + "\n".join(product_list)
        else:
            return f"Error: Received status code {response.status_code} while fetching products. Please try again."

    except requests.RequestException as e:
        logging.error(f"Error connecting to product database: {str(e)}")
        return f"Error connecting to the product database: {str(e)}"

@tool
def fund_customer(first_name: str, last_name: str, config: RunnableConfig) -> str:
    """Fund a customer account. This tool can only be used after customer verification."""
    logging.info(f"Calling fund_customer for: {first_name} {last_name}")
    try:
        # Extract context from config
        context = config.get("configurable", {}).get("context")
        if not context:
            logging.warning("No context found in config")
            return "I need to verify your identity first. Please provide your full name so I can look up your account."

        # Check if customer is verified by looking at context userdata
        if not hasattr(context, "userdata") or not hasattr(context.userdata, "customer_id"):
            return "I need to verify your identity before I can help you with funding. Please provide your full name so I can look up your account."

        # Verify the names match the verified customer
        verified_name = context.userdata.customer_name.lower()
        provided_name = f"{first_name} {last_name}".lower()

        if verified_name != provided_name:
            return f"The name you provided doesn't match your verified account. Please use your verified name: {context.userdata.customer_name}"

        # Prepare funding data
        funding_data = {
            'customerNumber': context.userdata.customer_id,
            'firstName': first_name,
            'lastName': last_name,
            'action': 'fund_account'
        }

        # Make request to fund customer account
        response = requests.post('http://localhost:5000/customers/fund', json=funding_data)

        if response.status_code == 200:
            funding_info = response.json()
            return f"Great! Your account has been successfully funded. Transaction ID: {funding_info.get('transactionId', 'N/A')}. Your current balance is ${funding_info.get('balance', '0.00')}."
        elif response.status_code == 400:
            error = response.json().get('error', 'Invalid funding request')
            return f"I apologize, but there was an issue with the funding request: {error}. Please try again."
        elif response.status_code == 404:
            return f"I apologize, but your customer account was not found. Please verify your identity again by providing your full name."
        else:
            return f"Error: Received status code {response.status_code} while processing your funding request. Please try again."

    except requests.RequestException as e:
        logging.error(f"Error connecting to funding system: {str(e)}")
        return f"Error connecting to the funding system: {str(e)}"
    except Exception as e:
        logging.error(f"Unexpected error in fund_customer: {str(e)}")
        return f"An unexpected error occurred: {str(e)}. Please try again."
