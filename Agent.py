import os
import re
import requests
from typing import Optional, Literal, Any, Dict
from langgraph.graph import StateGraph, MessagesState, START, END
from langgraph.types import Command
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import HumanMessage, AIMessage
from langchain_groq import ChatGroq
from langgraph.prebuilt import create_react_agent
import logging
from toolsAgent import find_customer, make_order, get_products, fund_customer

# Set up logging
logging.basicConfig(level=logging.INFO)

# Define RunContext_T for tools
RunContext_T = Dict[str, Any]

# State definition
class State(MessagesState):
    next: str
    customer_verified: bool = False
    customer_name: Optional[str] = None
    customer_id: Optional[str] = None
    last_attempted_name: Optional[str] = None
    verification_attempts: int = 0
    context: RunContext_T = {}  # To store userdata for tools

# Add a setter method for customer verification
def set_customer_verified(state: State, customer_name: str, customer_id: str) -> State:
    """
    Sets the customer as verified and updates all relevant state fields.

    Args:
        state: The current state object
        customer_name: The verified customer's full name
        customer_id: The verified customer's ID

    Returns:
        Updated state with verification information
    """
    # Create a copy of the state to avoid modifying the original
    updated_state = state.copy()

    # Set verification fields
    updated_state["customer_verified"] = True
    updated_state["customer_name"] = customer_name
    updated_state["customer_id"] = customer_id

    # Ensure context and userdata exist
    if "context" not in updated_state:
        updated_state["context"] = {}

    if "userdata" not in updated_state["context"]:
        updated_state["context"]["userdata"] = {}

    # Update context userdata
    updated_state["context"]["userdata"]["customer_name"] = customer_name
    updated_state["context"]["userdata"]["customer_id"] = customer_id

    # Log the verification
    logging.info(f"Customer verified: {customer_name} (ID: {customer_id})")

    return updated_state

# Initialize LLM
llm = ChatGroq(
    temperature=0.7,
    groq_api_key=os.getenv("GROQ_API_KEY", "********************************************************"),
    model_name="deepseek-r1-distill-llama-70b"
)

# Fetch product categories
try:
    response = requests.get('http://localhost:5000/productlines')
    if response.status_code == 200:
        product_categories = [item['productLine'] for item in response.json()]
    else:
        product_categories = []
except requests.RequestException as e:
    logging.error(f"Error fetching product categories: {str(e)}")
    product_categories = []

companyName = "Bitswits"

# Define Sales Agent
sales_agent = create_react_agent(
    llm,
    prompt=(
        f"""
You are a friendly and helpful Sales Agent named John Doe at our {companyName} company.
Begin by warmly welcoming the customer and introducing yourself.
Always remember do not make assumptions.
Let them know the product categories we currently offer: {', '.join(product_categories) if product_categories else 'None available at the moment'}.
Ask the customer which category they're interested in.

Once they select a category, use the 'get_products' tool to retrieve available products in that category.
Help the customer by describing a few options briefly. Don't list all products at once — just mention a couple at first.
If the customer doesn't like those, offer a few different ones from the same category.

Guide the customer through selecting a product.
Once a selection is made, use the 'make_order' tool to process their order with:
- The product name they selected
- The quantity they want to order

IMPORTANT:
- Be conversational and helpful.
- Do not overwhelm the customer with too many options at once.
- Focus on making the process smooth and friendly.
- Always confirm the order details before processing.
- Do not make assumptions; all product details come from the get_products tool.
Please do not use any formatting symbols like asterisks. Just reply with plain text, no Markdown or special characters.
"""
    ),
    tools=[get_products, make_order, fund_customer]
)

# Sales Agent Node
def Sales_Agent_node(state: State) -> Command[Literal["supervisor"]]:
    # Initialize context['userdata'] if not present
    if 'userdata' not in state["context"]:
        state["context"]["userdata"] = {}
    result = sales_agent.invoke(state)
    last_message = result["messages"][-1]
    return Command(
        update={"messages": [AIMessage(content=last_message.content, name="Sales_Agent")]},
        goto="supervisor"
    )

# Customer Verification Agent
customer_verification_agent = create_react_agent(
    llm,
    tools=[find_customer],
    prompt=(
        """
You are a customer verification assistant. Your job is to extract the customer's first name and last name from their message and use the find_customer tool to verify their identity in our system.
The find_customer tool requires two arguments: first_name and last_name.
If the user doesn't provide both first and last name, politely ask for this information.
Once you have verified the customer, inform them they've been verified and can proceed to Sales_Agent.
Do not ask follow-up questions beyond getting their name for verification.
"""
    ),
)

# Customer Tool Node
def tool_node(state: State) -> Command[Literal["supervisor"]]:
    """
    Handles customer verification using the customer_verification_agent.
    """
    logging.info(f"Current state: {state}")

    # Check if customer is already verified
    if state.get("customer_verified", False):
        logging.info("Customer already verified, proceeding to sales")
        return Command(
            update={"messages": [AIMessage(content="You are already verified! Proceeding to sales.", name="customer_tool")]},
            goto="supervisor"
        )

    # Initialize context['userdata'] if not present
    if 'context' not in state:
        state["context"] = {}

    if 'userdata' not in state["context"]:
        state["context"]["userdata"] = {}

    try:
        # Let the agent handle the verification
        logging.info("Invoking customer_verification_agent")
        result = customer_verification_agent.invoke(state)
        last_message = result["messages"][-1]
        logging.info(f"Agent result: {result}")

        # Check if verification was successful by examining the context
        if 'userdata' in result["context"] and 'customer_id' in result["context"]["userdata"]:
            # Extract customer info from context
            customer_name = result["context"]["userdata"]["customer_name"]
            customer_id = result["context"]["userdata"]["customer_id"]

            logging.info(f"Verification successful: {customer_name} (ID: {customer_id})")

            # Use the setter method to update state
            updated_state = set_customer_verified(state, customer_name, customer_id)

            # Return command with updated state
            return Command(
                update={
                    "messages": [AIMessage(content=last_message.content, name="customer_tool")],
                    "customer_verified": True,
                    "customer_name": customer_name,
                    "customer_id": customer_id,
                    "context": updated_state["context"]
                },
                goto="supervisor"
            )
        else:
            # Verification not successful yet
            logging.info("Verification not successful yet")
            return Command(
                update={"messages": [AIMessage(content=last_message.content, name="customer_tool")]},
                goto="supervisor"
            )

    except Exception as e:
        logging.error(f"Error in customer verification: {str(e)}")
        error_message = f"An error occurred during verification: {str(e)}. Please try again."
        return Command(
            update={"messages": [AIMessage(content=error_message, name="customer_tool")]},
            goto="supervisor"
        )

# Supervisor Node
def make_supervisor_node(llm: BaseChatModel, members: list[str]):
    """
    Creates a supervisor node that routes messages to the appropriate agent.
    """
    def supervisor_node(state: State) -> Command[Literal["Sales_Agent", "customer_tool", "__end__"]]:
        messages = state.get("messages", [])

        if not messages:
            return Command(goto="customer_tool", update={"next": "customer_tool"})

        last_message = messages[-1]

        # End if no user input is expected
        if not isinstance(last_message, HumanMessage):
            return Command(goto=END, update={})

        # Route to Sales_Agent if verified
        if state.get("customer_verified", False):
            return Command(goto="Sales_Agent", update={"next": "Sales_Agent"})

        # Limit verification attempts
        verification_attempts = state.get("verification_attempts", 0)
        if verification_attempts >= 3:
            return Command(
                update={"messages": [AIMessage(content="Maximum verification attempts reached. Please contact support.", name="customer_tool")]},
                goto=END
            )

        # Increment attempts and route to customer_tool
        return Command(
            update={"verification_attempts": verification_attempts + 1, "next": "customer_tool"},
            goto="customer_tool"
        )

    return supervisor_node

# Build Graph
supervisor_node = make_supervisor_node(llm, ["Sales_Agent", "customer_tool"])
research_builder = StateGraph(State)
research_builder.add_node("supervisor", supervisor_node)
research_builder.add_node("Sales_Agent", Sales_Agent_node)
research_builder.add_node("customer_tool", tool_node)
research_builder.add_edge(START, "supervisor")
research_graph = research_builder.compile()

# Print event stream
def _print_event(event, printed):
    messages = event.get("messages", [])
    for i, message in enumerate(messages):
        if i < len(printed):
            continue
        if hasattr(message, "content"):
            if hasattr(message, "name") and message.name:
                print(f"\n{message.name}: {message.content}")
            else:
                print(f"\nAI: {message.content}")
        printed.append(message)

# Run main loop
def main():
    print("Welcome to our customer service system. Type 'exit' to end the conversation.")
    conversation_state = {"messages": [], "context": {}}

    while True:
        user_input = input("\nHow can I help you today? ")

        if user_input.lower() in ['exit', 'quit', 'bye']:
            print("Thank you for using our service. Goodbye!")
            break

        conversation_state["messages"].append(HumanMessage(content=user_input))
        _printed = []

        try:
            events = research_graph.stream(conversation_state, stream_mode="values")
            for event in events:
                _print_event(event, _printed)
                conversation_state = event
        except Exception as e:
            logging.error(f"Error in conversation loop: {str(e)}")
            print("\nAn error occurred. Please try again or contact support.")

    return "Conversation completed"

if __name__ == "__main__":
    main()
